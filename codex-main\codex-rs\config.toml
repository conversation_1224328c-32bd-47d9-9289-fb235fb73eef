# Codex Configuration for Deepseek
model = "deepseek-chat, deepseek-reasoner"
model_provider = "deepseek"

# Approval policy - controls when user approval is required
approval_policy = "unless-allow-listed"  # Safe default: asks for approval for non-safe commands

# Deepseek model provider configuration
[model_providers.deepseek]
name = "Deepseek"
base_url = "https://api.deepseek.com/v1"
env_key = "***********************************"
wire_api = "chat"

# Sandbox permissions - controls what the AI can access
sandbox_permissions = [
    "disk-full-read-access",
    "disk-write-cwd",
    "disk-write-platform-user-temp-folder"
]

# Shell environment policy - controls environment variables passed to subprocesses
[shell_environment_policy]
inherit = "core"  # Only pass core environment variables
ignore_default_excludes = false  # Filter out sensitive variables like *KEY*, *TOKEN*
set = { DEEPSEEK_API_KEY = "***********************************" }

# Optional: Disable response storage for privacy (uncomment if needed)
# disable_response_storage = true

# Optional: Configure file opener for clickable links in output
file_opener = "vscode"

# Optional: Hide agent reasoning output for cleaner logs
# hide_agent_reasoning = true
